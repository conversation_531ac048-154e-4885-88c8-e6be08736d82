import pandas as pd
import json
from datetime import datetime
from DatabaseManagement.ImportExport import get_table_from_GZ
from IP.Trademarks_Bulk.trademark_db import get_table_from_db


def find_missing_trademarks_by_reg_no():
    
    print("=== Trademark Missing Checker (Registration Numbers) ===")
    print(f"Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Get cases from GZ database
    print("Fetching cases from GZ database...")
    df = get_table_from_GZ("tb_case", force_refresh=True)
    print(f"Loaded {len(df)} cases")
    
    # Get existing trademarks from PostgreSQL database
    print("Fetching existing trademarks from PostgreSQL database...")
    db_trademarks_df = get_table_from_db("trademarks")

    # Create set of existing reg_no values for fast lookup (only checking reg_no)
    existing_reg_nos = set()
    if not db_trademarks_df.empty:
        existing_reg_nos = set(db_trademarks_df['reg_no'].dropna().astype(str))

    print(f"Found {len(existing_reg_nos)} existing reg_no trademarks in database")
    
    # First, collect ALL unique reg_no values from tb_case
    print("First pass: Collecting all unique reg_no values from tb_case...")
    unique_trademarks_in_cases = {}  # Will store unique trademark info keyed by reg_no
    unique_reg_nos_in_cases = set()
    processed_cases = 0
    total_trademark_entries = 0

    # Process each case to collect unique trademarks
    for _, row in df.iterrows():
        processed_cases += 1

        # Get trademarks from images
        trademarks = (row.get('images') or {}).get('trademarks', {})
        if not trademarks:
            continue

        # Process each trademark
        for image_name, trademark_info in trademarks.items():
            total_trademark_entries += 1

            if not isinstance(trademark_info, dict):
                continue

            # Get registration numbers only (we're only checking reg_no)
            reg_numbers = trademark_info.get('reg_no', [])
            if not isinstance(reg_numbers, list):
                reg_numbers = [reg_numbers] if reg_numbers else []

            # Clean and prepare reg_no values for checking
            clean_reg_numbers = []
            for reg_no in reg_numbers:
                if reg_no:
                    clean_reg_no = str(reg_no).strip()
                    if clean_reg_no:
                        clean_reg_numbers.append(clean_reg_no)
                        unique_reg_nos_in_cases.add(clean_reg_no)

            # Store each unique reg_no separately
            for reg_no in clean_reg_numbers:
                unique_key = reg_no

                # Store the first occurrence of this unique reg_no
                if unique_key not in unique_trademarks_in_cases:
                    unique_trademarks_in_cases[unique_key] = {
                        'plaintiff_id': row.get('plaintiff_id', 'Unknown'),
                        'docket': row.get('docket', 'Unknown'),
                        'case_id': row.get('id', 'Unknown'),
                        'reg_no': reg_no,
                        'all_reg_nos': clean_reg_numbers,
                    }

    print(f"\n=== UNIQUE REG_NO VALUES IN TB_CASE ===")
    print(f"Total unique reg_no values found in tb_case: {len(unique_trademarks_in_cases)}")
    print(f"Unique reg_nos in tb_case: {len(unique_reg_nos_in_cases)}")

    # Now check which unique reg_no values are missing from the database
    print(f"\nSecond pass: Checking which unique reg_no values are missing from database...")
    missing_trademarks = []
    missing_count = 0

    for reg_no, trademark_data in unique_trademarks_in_cases.items():
        # Check if this reg_no exists in database
        trademark_exists = reg_no in existing_reg_nos

        # If trademark doesn't exist in database, add it to missing list
        if not trademark_exists:
            missing_count += 1

            missing_trademarks.append({
                'plaintiff_id': trademark_data['plaintiff_id'],
                'docket': trademark_data['docket'],
                'case_id': trademark_data['case_id'],
                'reg_no': reg_no,
                'all_reg_nos': ', '.join(trademark_data['all_reg_nos']) if trademark_data['all_reg_nos'] else 'None',
            })

            # Print progress for missing trademarks
            if missing_count % 100 == 0:
                print(f"Found {missing_count} unique missing reg_no values so far...")

    # Print summary
    print(f"\n=== FINAL SUMMARY ===")
    print(f"Processed cases: {processed_cases}")
    print(f"Total trademark entries in cases: {total_trademark_entries}")
    print(f"Unique reg_no values in tb_case: {len(unique_trademarks_in_cases)}")
    print(f"Unique reg_nos in tb_case: {len(unique_reg_nos_in_cases)}")
    print(f"Missing reg_no values: {missing_count}")
    print(f"Percentage missing: {(missing_count / len(unique_trademarks_in_cases) * 100):.1f}%" if unique_trademarks_in_cases else "N/A")

    # Save missing trademarks to CSV
    if missing_trademarks:
        df_missing = pd.DataFrame(missing_trademarks)
        output_file = 'unique_missing_trademarks_by_reg_no.csv'
        df_missing.to_csv(output_file, index=False)
        print(f"\nUnique missing trademarks saved to: {output_file}")
        print(f"Structure issues log saved to: structure_issues.log")

    else:
        print("No missing trademarks found!")
    
    print(f"\nCompleted at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    return missing_trademarks

if __name__ == "__main__":
    find_missing_trademarks_by_reg_no()