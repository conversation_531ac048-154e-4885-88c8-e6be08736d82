import sys
import os
from datetime import datetime
from contextlib import contextmanager

class TeeOutput:
    def __init__(self, filename):
        self.file = open(filename, 'a', encoding='utf-8')
        self.stdout = sys.stdout
        self.stderr = sys.stderr

    def write(self, data):
        self.file.write(data)
        self.stdout.write(data)
        self.file.flush()
        self.stdout.flush()

    def flush(self):
        self.file.flush()
        self.stdout.flush()

    def close(self):
        if self.file:
            self.file.close()

@contextmanager
def capture_output(log_file):
    tee = TeeOutput(log_file)
    old_stdout = sys.stdout
    old_stderr = sys.stderr
    try:
        sys.stdout = tee
        sys.stderr = tee
        yield
    finally:
        sys.stdout = old_stdout
        sys.stderr = old_stderr
        tee.close()

def get_trademark_log_file():
    """Generate a log file path with timestamp."""
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    log_dir = os.path.join('/app', 'logs', 'trademarks')
    os.makedirs(log_dir, exist_ok=True)
    return os.path.join(log_dir, f'trademark_api_{timestamp}.log')
