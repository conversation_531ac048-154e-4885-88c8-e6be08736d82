import asyncio
from IP.Trademarks.Trademark_API import get_trademarks_uspto
from IP.Trademarks.trademark_logger import capture_output, get_trademark_log_file
from datetime import datetime
import pandas as pd

async def get_trademarks_uspto_with_logging(test_reg_numbers, case_date=None, plaintiff_names=None):
    """
    Wrapper function that adds logging to get_trademarks_uspto without modifying the original function.
    """
    log_file = get_trademark_log_file()
    
    with capture_output(log_file):
        print(f"Starting trademark processing at {datetime.now()}")
        print(f"Processing {len(test_reg_numbers)} trademark records")
        
        # Call the original function
        result = await get_trademarks_uspto(test_reg_numbers)
        
        print(f"Completed trademark processing at {datetime.now()}")
        print(f"Processed {len(result)} trademarks")
        
    return result

# Usage example:
if __name__ == "__main__":
    test = pd.read_csv("/app/z.csv")
    test = test.to_dict(orient='records')
    
    df_result = asyncio.run(get_trademarks_uspto_with_logging(test))
    print(f"Processed {len(df_result)} trademarks")
