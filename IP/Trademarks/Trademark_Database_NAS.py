import gc
import zlib, base64, json, os, cv2, time, fitz, zipfile, asyncio, shutil, io, re, time, multiprocessing
import pandas as pd
from PIL import Image
import tempfile
from multiprocessing import Manager
import uuid
from datetime import datetime
from DatabaseManagement.Connections import get_gz_connection, is_connection_alive
from DatabaseManagement.ImportExport import get_table_from_GZ, insert_and_update_df_to_GZ_batch
from Alerts.PicturesProcessing.ProcessPicturesShared import convert_page_number_to_image
start_time = time.time()
from IP.Trademarks.USPTO_TSDR_API import TSDRApi, format_reg_number
if multiprocessing.current_process().name == 'MainProcess':
    print(f"                TrademarkAPI TSDRApi after  {time.time()-start_time:.2f} seconds")
from Common.Constants import local_ip_folder, nas_ip_folder, sem_task
from FileManagement.NAS import NASConnection
from psycopg2.extras import Json

# Import the helper function from Trademark_API to avoid circular imports
# We'll import it when needed in the functions that use it

# ❌⚠️📥
def sanitize_array(arr):
    if not isinstance(arr, list):
        return arr
    sanitized = []
    for elem in arr:
        if isinstance(elem, dict):
            sanitized.append(json.dumps(elem))
        else:
            sanitized.append(elem)
    return sanitized

async def save_trademark_to_database(formatted_reg_no, new_row, db_connection, current_trademark_cache_df):
    """
    Saves a new trademark to the PostgreSQL database.
    """    
    try:
        # Only save if we have meaningful data
        if not new_row.get("reg_no") or not new_row.get("ser_no"):
            print(f"⚠️ Insufficient data for trademark {formatted_reg_no}, not saving to database")
            return False
        
        # Create a copy for database storage, removing metadata which is not stored in DB
        db_row = new_row.copy()
        metadata = db_row.pop("metadata", None)  # Remove metadata if present
        
        try:
            with db_connection.cursor() as cursor:
                # Check if trademark already exists to avoid duplicates
                cursor.execute(
                    "SELECT id FROM trademarks WHERE reg_no = %s OR ser_no = %s",
                    (db_row.get("reg_no"), db_row.get("ser_no"))
                )
                existing = cursor.fetchone()
                
                if existing:
                    print(f"⚠️ Trademark {formatted_reg_no} already exists in database, skipping")
                    return Exception
                
                # Prepare data for insertion
                final_data = {
                    "id": str(uuid.uuid4()),
                    "reg_no": new_row.get("reg_no"),
                    "ser_no": new_row.get("ser_no"),
                    "tro": True,
                    "applicant_name": new_row.get("applicant_name"),
                    "mark_text": new_row.get("text"),
                    "int_cls": sanitize_array(new_row.get("int_cls")),
                    "filing_date": new_row.get("date"),
                    "plaintiff_id": new_row.get("plaintiff_id"),
                    "nb_suits": new_row.get("nb_suits"),
                    "country_codes": sanitize_array(new_row.get("country_codes")),
                    "associated_marks": sanitize_array(new_row.get("associated_marks")),
                    "info_source": new_row.get("info_source"),
                    "image_source": new_row.get("image_source"),
                    "certificate_source": new_row.get("certificate_source"),
                    "mark_current_status_code": new_row.get("mark_current_status_code"),
                    "mark_feature_code": new_row.get("mark_feature_code"),
                    "mark_standard_character_indicator": new_row.get("mark_standard_character_indicator"),
                    "mark_disclaimer_text": sanitize_array(new_row.get("mark_disclaimer_text")),
                    "mark_disclaimer_text_daily": sanitize_array(new_row.get("mark_disclaimer_text_daily")),
                    "mark_image_colour_claimed_text": new_row.get("mark_image_colour_claimed_text"),
                    "mark_image_colour_part_claimed_text": new_row.get("mark_image_colour_part_claimed_text"),
                    "mark_image_colour_statement_daily": sanitize_array(new_row.get("mark_image_colour_statement_daily")),
                    "mark_translation_statement_daily": new_row.get("mark_translation_statement_daily"),
                    "name_portrait_statement_daily": new_row.get("name_portrait_statement_daily"),
                    "mark_description_statement_daily": new_row.get("mark_description_statement_daily"),
                    "certification_mark_statement_daily": new_row.get("certification_mark_statement_daily"),
                    "lining_stippling_statement_daily": new_row.get("lining_stippling_statement_daily"),
                    "section_2f_statement_daily": new_row.get("section_2f_statement_daily"),
                    "national_design_code": sanitize_array(new_row.get("national_design_code")),
                    "goods_services": Json(new_row.get("goods_services")),  # jsonb column
                    "goods_services_text_daily": new_row.get("goods_services_text_daily"),
                    "case_file_statements_other": Json(new_row.get("case_file_statements_other")),  # jsonb column
                    "mark_current_status_external_description_text": new_row.get("mark_current_status_external_description_text"),
                    "create_time": datetime.now(),
                    "update_time": datetime.now()
                }
                
                # Convert date string to date object if needed
                if isinstance(final_data["filing_date"], str):
                    try:
                        final_data["filing_date"] = datetime.strptime(final_data["filing_date"], "%Y-%m-%d").date()
                    except:
                        final_data["filing_date"] = None
                
                # Build INSERT query
                columns = [col for col, val in final_data.items() if val is not None]
                placeholders = ", ".join(["%s"] * len(columns))
                column_names = ", ".join(columns)
                
                insert_query = f"INSERT INTO trademarks ({column_names}) VALUES ({placeholders})"
                
                values = [final_data[col] for col in columns]
                
                cursor.execute(insert_query, values)
                db_connection.commit()
                
            # Add to the dataframe cache (convert back to original format for compatibility)
            db_row_for_df = {
                "reg_no": final_data["reg_no"],
                "ser_no": final_data["ser_no"],
                "TRO": final_data["tro"],
                "applicant_name": final_data["applicant_name"],
                "text": final_data["mark_text"],
                "int_cls": final_data["int_cls"],
                "date": final_data["filing_date"],
                "nb_suits": final_data["nb_suits"],
                "country_codes": final_data["country_codes"],
                "associated_marks": final_data["associated_marks"],
                "info_source": final_data["info_source"],
                "image_source": final_data["image_source"],
                "certificate_source": final_data["certificate_source"],
                "plaintiff_id": final_data["plaintiff_id"]
            }
            
            # Add back metadata for the DataFrame version if it existed
            if metadata:
                db_row_for_df["metadata"] = metadata
                
            # Add new row to current_trademark_cache_df
            if current_trademark_cache_df is not None:
                current_trademark_cache_df = pd.concat([current_trademark_cache_df, pd.DataFrame([db_row_for_df])], ignore_index=True)
                
            print(f"✅ Added trademark {formatted_reg_no} to PostgreSQL database")
            return True
            
        except Exception as e:
            print(f"🔥 Error saving trademark to database: {str(e)}")
            db_connection.rollback()  # Rollback on error
            return False
            
    except Exception as e:
        print(f"🔥 Error preparing trademark data for database: {str(e)}")
        return False

async def send_trademark_to_nas(formatted_reg_no, nas, local_ip_folder, nas_ip_folder, ser_no=None):
    """
    Sends a single trademark's files to the NAS.

    Args:
        formatted_reg_no: Formatted registration number
        nas: NASConnection object
        local_ip_folder: Local IP folder path
        nas_ip_folder: NAS IP folder path
        ser_no: Serial number (optional, for new certificate path structure)

    Returns:
        bool: True if transfer was successful
    """
    try:
        # Define local paths
        local_xml_path = os.path.join(local_ip_folder, "Trademarks", "XML", f"{formatted_reg_no}.xml")
        local_image_path = os.path.join(local_ip_folder, "Trademarks", "Images", f"{formatted_reg_no}.webp")
        # Use new certificate path structure
        from IP.Trademarks.Trademark_API import get_certificate_local_path
        certificate_base_folder = os.path.join(local_ip_folder, "Trademarks", "Certificates")
        local_certificate_path = get_certificate_local_path(certificate_base_folder, ser_no=ser_no, reg_no=formatted_reg_no)

        # Define NAS paths (keeping old structure for NAS for now)
        # TODO: Update NAS structure when ser_no is available in this function
        nas_xml_path = f"{nas_ip_folder}/Trademarks/XML/{formatted_reg_no}.xml"
        nas_image_path = f"{nas_ip_folder}/Trademarks/Images/{formatted_reg_no}.webp"
        nas_certificate_path = f"{nas_ip_folder}/Trademarks/Certificates/{formatted_reg_no}.webp"
        
        if os.path.exists(local_xml_path) and not nas.ssh_exists(nas_xml_path):
            await asyncio.to_thread(nas.transfer_file_with_scp, local_path=local_xml_path, remote_path=nas_xml_path, to_nas=True)
            print(f"☑️ Transferred trademark XML {formatted_reg_no} to NAS")
            
        if os.path.exists(local_image_path) and not nas.ssh_exists(nas_image_path):
            await asyncio.to_thread(nas.transfer_file_with_scp, local_path=local_image_path, remote_path=nas_image_path, to_nas=True)
            print(f"☑️ Transferred trademark IMAGE {formatted_reg_no} to NAS")
            
        if os.path.exists(local_certificate_path) and not nas.ssh_exists(nas_certificate_path):
            await asyncio.to_thread(nas.transfer_file_with_scp, local_path=local_certificate_path, remote_path=nas_certificate_path, to_nas=True)
            print(f"☑️ Transferred trademark CERTIFICATE {formatted_reg_no} to NAS")
            
        return True
    except Exception as e:
        print(f"🔥 Error sending trademark {formatted_reg_no} to NAS: {str(e)}")
        return False


async def update_trademark_plaintiff_id(formatted_reg_no, plaintiff_id, db_connection):
    if not plaintiff_id:
        return False # Nothing to update
    try:
        with db_connection.cursor() as cursor:
            cursor.execute(
                "UPDATE trademarks SET plaintiff_id = %s, tro = TRUE, update_time = NOW() WHERE reg_no = %s AND (plaintiff_id IS NULL OR plaintiff_id != %s)",
                (int(plaintiff_id), formatted_reg_no, int(plaintiff_id))
            )
            db_connection.commit()
            return True
    except Exception as e:
        print(f"🔥 Error updating plaintiff_id for trademark {formatted_reg_no}: {str(e)}")
        db_connection.rollback()
        return False


def get_trademark_from_NAS(formatted_reg_no, df_entry, nas, ser_no=None):
    """
    Retrieves trademark files from local storage or NAS if needed.

    Args:
        formatted_reg_no: Formatted registration number
        df_entry: Dataframe entry for the trademark
        nas: NASConnection object
        ser_no: Serial number (optional, for new certificate path structure)

    Returns:
        tuple: (found_bool, xml_path, image_path, certificate_path)
    """
    # Get files from local or NAS
    xml_local_path = os.path.join(local_ip_folder, "Trademarks", "XML", f"{formatted_reg_no}.xml")
    image_local_path = os.path.join(local_ip_folder, "Trademarks", "Images", f"{formatted_reg_no}.webp")
    # Use new certificate path structure
    from IP.Trademarks.Trademark_API import get_certificate_local_path
    certificate_base_folder = os.path.join(local_ip_folder, "Trademarks", "Certificates")
    certificate_local_path = get_certificate_local_path(certificate_base_folder, ser_no=ser_no, reg_no=formatted_reg_no)
    
    # Check if all required files exist locally
    xml_exists = os.path.exists(xml_local_path)
    image_exists = os.path.exists(image_local_path)
    certificate_exists = os.path.exists(certificate_local_path)
    
    # If both main files exist locally, return success
    if xml_exists and image_exists and certificate_exists:
        print(f"☑️ Trademark {formatted_reg_no} files found locally")
        return True, xml_local_path, image_local_path, certificate_local_path
    
    # If not, try to get from NAS
    files_transferred = False
    
    # Check which files we need to get from NAS
    nas_xml_path = f"{nas_ip_folder}/Trademarks/XML/{formatted_reg_no}.xml"
    nas_image_path = f"{nas_ip_folder}/Trademarks/Images/{formatted_reg_no}.webp"
    nas_certificate_path = f"{nas_ip_folder}/Trademarks/Certificates/{formatted_reg_no}.webp"
    
    # Create local directories if they don't exist
    os.makedirs(os.path.dirname(xml_local_path), exist_ok=True)
    os.makedirs(os.path.dirname(image_local_path), exist_ok=True)
    os.makedirs(os.path.dirname(certificate_local_path), exist_ok=True)
    
    # Transfer XML if needed
    if not xml_exists and nas.ssh_exists(nas_xml_path):
        nas.transfer_file_with_scp(
            local_path=xml_local_path,
            remote_path=nas_xml_path,
            to_nas=False
        )
        files_transferred = True
        xml_exists = True
    
    # Transfer image if needed
    if not image_exists and nas.ssh_exists(nas_image_path):
        nas.transfer_file_with_scp(
            local_path=image_local_path,
            remote_path=nas_image_path,
            to_nas=False
        )
        files_transferred = True
        image_exists = True
    
    # Transfer certificate if it exists
    if not certificate_exists and nas.ssh_exists(nas_certificate_path):
        nas.transfer_file_with_scp(
            local_path=certificate_local_path,
            remote_path=nas_certificate_path,
            to_nas=False
        )
        files_transferred = True
        certificate_exists = True
    
    if files_transferred:
        print(f"☑️ Retrieved trademark {formatted_reg_no} files from NAS")
    
    # Check if we have the essential files now
    if xml_exists and image_exists and certificate_exists:
        return True, xml_local_path, image_local_path, certificate_local_path
    
    print(f"❌ Could not find complete files for trademark {formatted_reg_no}")
    return False, None, None, None



def create_full_list_of_reg_no(df_cases):
    api_client = TSDRApi()
    nas = NASConnection()
    full_list_of_reg_no = []
    certificates_folder = os.path.join(local_ip_folder, "Trademarks", "CertificatesFromTRO_ALL")
    os.makedirs(certificates_folder, exist_ok=True)
    for index, row in df_cases.iterrows():
        plaintiff_id = row["plaintiff_id"]
        if "trademarks" in row["images"].keys():
            for key, value in row["images"]["trademarks"].items():
                for i, reg_no in enumerate(row["images"]["trademarks"][key]["reg_no"]):
                    if reg_no != "":
                        # !!!!! How to know which one is which?
                        formatted_reg_no = format_reg_number(reg_no, id_key='rn')

                        # destination = os.path.join(certificates_folder, f"{formatted_reg_no}.webp")
                        # if not os.path.exists(destination):
                        #     sanitized_case_name = sanitize_name(f"{pd.to_datetime(row['date_filed'], errors='coerce').strftime('%Y-%m-%d')} - {row['docket']}")
                        #     source = os.path.join(local_case_folder, sanitized_case_name, "images", row["images"]["trademarks"][key]["full_filename"][i])
                            
                        #     if os.path.exists(source):
                        #         shutil.copy(source, destination)
                        #     else:
                        #         print(f"🔥 Error: Source file {source} does not exist => copying the folder from NAS")
                        #         nas.ssh_nas_to_local(f"{nas_case_folder}/{sanitized_case_name}", os.path.join(local_case_folder, sanitized_case_name))
                        #         if os.path.exists(source):
                        #             shutil.copy(source, destination)
                        #         else:
                        #             print(f"🔥🔥🔥 Error: Source file {source} does not exist after copying the folder from NAS")
                        
                        
                        # Add to the list of registration numbers with plaintiff_id
                        full_list_of_reg_no.append({"plaintiff_id": plaintiff_id, "reg_no": formatted_reg_no})

    # Remove duplicates based on reg_no while preserving the first occurrence
    seen_reg_nos = set()
    unique_list = []
    for item in full_list_of_reg_no:
        if item["reg_no"] not in seen_reg_nos:
            seen_reg_nos.add(item["reg_no"])
            unique_list.append(item)

    print(f"There are {len(unique_list)} trademarks to scrape.")
    return unique_list


if __name__ == "__main__":
    df_cases = get_table_from_GZ("tb_case", force_refresh=False)
    full_list_of_reg_no = create_full_list_of_reg_no(df_cases)