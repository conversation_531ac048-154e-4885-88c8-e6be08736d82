#!/usr/bin/env python3
from DatabaseManagement.ImportExport import get_table_from_GZ

def count_trademarks():
    df = get_table_from_GZ("tb_case", force_refresh=True)

    unique_ser_nos = set()
    unique_reg_nos = set()

    for _, row in df.iterrows():
        if row['images'] and isinstance(row['images'], dict):
            trademarks = row['images'].get('trademarks', {})
            if trademarks and isinstance(trademarks, dict):
                for trademark_info in trademarks.values():
                    if isinstance(trademark_info, dict):
                        # Collect unique ser_nos
                        ser_nos = trademark_info.get('ser_no', [])
                        if not isinstance(ser_nos, list):
                            ser_nos = [ser_nos] if ser_nos else []
                        for s in ser_nos:
                            if s:
                                unique_ser_nos.add(str(s).strip())

                        # Collect unique reg_nos
                        reg_nos = trademark_info.get('reg_no', [])
                        if not isinstance(reg_nos, list):
                            reg_nos = [reg_nos] if reg_nos else []
                        for r in reg_nos:
                            if r:
                                unique_reg_nos.add(str(r).strip())

    print(f"Unique ser_nos: {len(unique_ser_nos)}")
    print(f"Unique reg_nos: {len(unique_reg_nos)}")

if __name__ == "__main__":
    count_trademarks()
